#include "Drawing.h"
#include "MemoryMonitor.h"
#include "APIHook.h"
#include <sstream>
#include <iomanip>
#include <fstream>

LPCSTR Drawing::lpWindowName = "Array of Bytes Monitor - Cheat Engine Detection";
ImVec2 Drawing::vWindowSize = { 900, 700 };
ImGuiWindowFlags Drawing::WindowFlags = 0;
bool Drawing::bDraw = true;

// Static variables for UI state
static bool showAddRegionDialog = false;
static char addressInput[32] = "";
static char sizeInput[16] = "4096";
static bool autoScroll = true;
static int maxLogEntries = 1000;

void Drawing::Active()
{
	bDraw = true;
}

bool Drawing::isActive()
{
	return bDraw == true;
}

void Drawing::Draw()
{
	if (isActive())
	{
		ImGui::SetNextWindowSize(vWindowSize, ImGuiCond_Once);
		ImGui::SetNextWindowBgAlpha(1.0f);
		ImGui::Begin(lpWindowName, &bDraw, WindowFlags);
		{
			// Status section
			ImGui::Text("Array of Bytes Monitor - Detecting your Cheat Engine modifications");
			ImGui::Text("Search & Replace operations will be captured here");
			ImGui::Separator();

			// Monitor status
			if (MemoryMonitor::IsMonitoring()) {
				ImGui::TextColored(ImVec4(0, 1, 0, 1), "Status: MONITORING ACTIVE");
			} else {
				ImGui::TextColored(ImVec4(1, 0, 0, 1), "Status: MONITORING STOPPED");
			}

			ImGui::SameLine();
			ImGui::Text("| Process ID: %d", MemoryMonitor::GetTargetProcessId());
			ImGui::SameLine();
			ImGui::Text("| Memory Writes: %zu", MemoryMonitor::GetLogCount());

			// Control buttons
			ImGui::Separator();
			if (ImGui::Button("Start Memory Monitoring")) {
				if (!MemoryMonitor::IsMonitoring()) {
					MemoryMonitor::Initialize();
					MemoryMonitor::StartMonitoring();
				}
			}
			ImGui::SameLine();
			if (ImGui::Button("Stop Monitoring")) {
				MemoryMonitor::StopMonitoring();
			}
			ImGui::SameLine();
			if (ImGui::Button("Clear Log")) {
				MemoryMonitor::ClearWriteLog();
			}
			ImGui::SameLine();
			if (ImGui::Button("Save Log")) {
				// Save API log to file
				std::ofstream file("api_log.txt");
				if (file.is_open()) {
					const auto& log = APIHook::GetAPILog();
					for (const auto& entry : log) {
						file << entry.timestamp << " | " << entry.apiName << " | "
							 << APIHook::FormatAddress(entry.targetAddress) << " | Size: " << entry.size;
						if (!entry.data.empty()) {
							file << " | Data: " << APIHook::FormatBytes(entry.data);
						}
						file << "\n";
					}
					file.close();
				}
			}

			// Add Region Dialog
			if (showAddRegionDialog) {
				ImGui::OpenPopup("Add Memory Region");
			}

			if (ImGui::BeginPopupModal("Add Memory Region", &showAddRegionDialog, ImGuiWindowFlags_AlwaysAutoResize)) {
				ImGui::Text("Enter memory region to monitor:");
				ImGui::InputText("Address (hex)", addressInput, sizeof(addressInput));
				ImGui::InputText("Size (bytes)", sizeInput, sizeof(sizeInput));

				if (ImGui::Button("Add")) {
					try {
						PVOID address = (PVOID)std::stoull(addressInput, nullptr, 16);
						SIZE_T size = std::stoull(sizeInput);
						if (MemoryMonitor::AddMemoryRegion(address, size)) {
							showAddRegionDialog = false;
							memset(addressInput, 0, sizeof(addressInput));
							strcpy_s(sizeInput, "4096");
						}
					} catch (...) {
						// Invalid input
					}
				}
				ImGui::SameLine();
				if (ImGui::Button("Cancel")) {
					showAddRegionDialog = false;
				}
				ImGui::EndPopup();
			}

			// Settings and Debug Info
			ImGui::Separator();
			ImGui::Checkbox("Auto Scroll", &autoScroll);
			ImGui::SameLine();
			ImGui::SliderInt("Max Log Entries", &maxLogEntries, 100, 10000);

			// Debug information
			ImGui::Text("Debug Info:");
			ImGui::Text("Current Process: %d", GetCurrentProcessId());
			ImGui::Text("Thread ID: %d", GetCurrentThreadId());

			// Test button for manual memory write
			if (ImGui::Button("Test Memory Write")) {
				static int testValue = 0;
				testValue++;
				// This should trigger our monitoring if a region is added
			}

			// Memory modifications log
			ImGui::Separator();
			ImGui::Text("Live Memory Write Detection:");
			ImGui::Text("Captures ANY memory modification from Cheat Engine or other tools");

			if (ImGui::BeginChild("LogRegion", ImVec2(0, 400), true)) {
				const auto& writeLog = MemoryMonitor::GetWriteLog();

				// Table headers
				if (ImGui::BeginTable("WriteLogTable", 6, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_ScrollY)) {
					ImGui::TableSetupColumn("Time", ImGuiTableColumnFlags_WidthFixed, 80);
					ImGui::TableSetupColumn("Address", ImGuiTableColumnFlags_WidthFixed, 120);
					ImGui::TableSetupColumn("Old Bytes", ImGuiTableColumnFlags_WidthStretch);
					ImGui::TableSetupColumn("New Bytes", ImGuiTableColumnFlags_WidthStretch);
					ImGui::TableSetupColumn("Size", ImGuiTableColumnFlags_WidthFixed, 60);
					ImGui::TableSetupColumn("Thread", ImGuiTableColumnFlags_WidthFixed, 60);
					ImGui::TableHeadersRow();

					// Display log entries (newest first)
					for (int i = (int)writeLog.size() - 1; i >= 0 && i >= (int)writeLog.size() - maxLogEntries; --i) {
						const auto& entry = writeLog[i];
						ImGui::TableNextRow();

						ImGui::TableNextColumn();
						ImGui::Text("%s", entry.timestamp.c_str());

						ImGui::TableNextColumn();
						// Highlight the address in yellow
						ImGui::TextColored(ImVec4(1, 1, 0, 1), "%s", MemoryMonitor::FormatAddress(entry.address).c_str());

						ImGui::TableNextColumn();
						// Show old bytes in red
						ImGui::TextColored(ImVec4(1, 0.5f, 0.5f, 1), "%s", MemoryMonitor::FormatBytes(entry.oldBytes, 16).c_str());

						ImGui::TableNextColumn();
						// Show new bytes in cyan
						ImGui::TextColored(ImVec4(0.5f, 1, 1, 1), "%s", MemoryMonitor::FormatBytes(entry.newBytes, 16).c_str());

						ImGui::TableNextColumn();
						ImGui::Text("%zu", entry.size);

						ImGui::TableNextColumn();
						ImGui::Text("%d", entry.threadId);
					}

					if (autoScroll && !writeLog.empty()) {
						ImGui::SetScrollHereY(1.0f);
					}

					ImGui::EndTable();
				}
			}
			ImGui::EndChild();

			ImGui::Separator();

			// Statistics
			const auto& writeLog = MemoryMonitor::GetWriteLog();
			if (!writeLog.empty()) {
				ImGui::Text("Statistics:");
				ImGui::Text("Total Memory Writes Detected: %zu", writeLog.size());

				if (!writeLog.empty()) {
					ImGui::Text("Last Write: %s at %s",
						MemoryMonitor::FormatAddress(writeLog.back().address).c_str(),
						writeLog.back().timestamp.c_str());
				}
			}

			ImGui::Separator();
			ImGui::Text("Performance: %.3f ms/frame (%.1f FPS)", 1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
			ImGui::Text("Ready to capture your Cheat Engine modifications!");
		}
		ImGui::End();
	}

	#ifdef _WINDLL
	if (GetAsyncKeyState(VK_INSERT) & 1)
		bDraw = !bDraw;
	#endif
}
