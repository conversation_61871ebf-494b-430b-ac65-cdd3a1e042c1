#include "Drawing.h"
#include "MemoryMonitor.h"
#include <sstream>
#include <iomanip>

LPCSTR Drawing::lpWindowName = "Memory Write Logger";
ImVec2 Drawing::vWindowSize = { 800, 600 };
ImGuiWindowFlags Drawing::WindowFlags = 0;
bool Drawing::bDraw = true;

// Static variables for UI state
static bool showAddRegionDialog = false;
static char addressInput[32] = "";
static char sizeInput[16] = "4096";
static bool autoScroll = true;
static int maxLogEntries = 1000;

void Drawing::Active()
{
	bDraw = true;
}

bool Drawing::isActive()
{
	return bDraw == true;
}

void Drawing::Draw()
{
	if (isActive())
	{
		ImGui::SetNextWindowSize(vWindowSize, ImGuiCond_Once);
		ImGui::SetNextWindowBgAlpha(1.0f);
		ImGui::Begin(lpWindowName, &bDraw, WindowFlags);
		{
			// Status section
			ImGui::Text("Memory Write Logger - Free Fire Edition");
			ImGui::Separator();

			// Monitor status
			if (MemoryMonitor::IsMonitoring()) {
				ImGui::TextColored(ImVec4(0, 1, 0, 1), "Status: MONITORING");
			} else {
				ImGui::TextColored(ImVec4(1, 0, 0, 1), "Status: STOPPED");
			}

			ImGui::SameLine();
			ImGui::Text("| Process ID: %d", MemoryMonitor::GetTargetProcessId());
			ImGui::SameLine();
			ImGui::Text("| Regions: %zu", MemoryMonitor::GetMonitoredRegionCount());
			ImGui::SameLine();
			ImGui::Text("| Log Entries: %zu", MemoryMonitor::GetLogCount());

			// Control buttons
			ImGui::Separator();
			if (ImGui::Button("Start Monitoring")) {
				if (!MemoryMonitor::IsMonitoring()) {
					MemoryMonitor::Initialize();
					MemoryMonitor::StartMonitoring();
				}
			}
			ImGui::SameLine();
			if (ImGui::Button("Stop Monitoring")) {
				MemoryMonitor::StopMonitoring();
			}
			ImGui::SameLine();
			if (ImGui::Button("Clear Log")) {
				MemoryMonitor::ClearWriteLog();
			}
			ImGui::SameLine();
			if (ImGui::Button("Add Region")) {
				showAddRegionDialog = true;
			}
			ImGui::SameLine();
			if (ImGui::Button("Save Log")) {
				MemoryMonitor::SaveLogToFile("memory_log.txt");
			}

			// Add Region Dialog
			if (showAddRegionDialog) {
				ImGui::OpenPopup("Add Memory Region");
			}

			if (ImGui::BeginPopupModal("Add Memory Region", &showAddRegionDialog, ImGuiWindowFlags_AlwaysAutoResize)) {
				ImGui::Text("Enter memory region to monitor:");
				ImGui::InputText("Address (hex)", addressInput, sizeof(addressInput));
				ImGui::InputText("Size (bytes)", sizeInput, sizeof(sizeInput));

				if (ImGui::Button("Add")) {
					try {
						PVOID address = (PVOID)std::stoull(addressInput, nullptr, 16);
						SIZE_T size = std::stoull(sizeInput);
						if (MemoryMonitor::AddMemoryRegion(address, size)) {
							showAddRegionDialog = false;
							memset(addressInput, 0, sizeof(addressInput));
							strcpy_s(sizeInput, "4096");
						}
					} catch (...) {
						// Invalid input
					}
				}
				ImGui::SameLine();
				if (ImGui::Button("Cancel")) {
					showAddRegionDialog = false;
				}
				ImGui::EndPopup();
			}

			// Settings
			ImGui::Separator();
			ImGui::Checkbox("Auto Scroll", &autoScroll);
			ImGui::SameLine();
			ImGui::SliderInt("Max Log Entries", &maxLogEntries, 100, 10000);

			// Memory writes log
			ImGui::Separator();
			ImGui::Text("Memory Write Log:");

			if (ImGui::BeginChild("LogRegion", ImVec2(0, 300), true)) {
				const auto& writeLog = MemoryMonitor::GetWriteLog();

				// Table headers
				if (ImGui::BeginTable("WriteLogTable", 5, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_ScrollY)) {
					ImGui::TableSetupColumn("Time", ImGuiTableColumnFlags_WidthFixed, 80);
					ImGui::TableSetupColumn("Address", ImGuiTableColumnFlags_WidthFixed, 120);
					ImGui::TableSetupColumn("Old Bytes", ImGuiTableColumnFlags_WidthStretch);
					ImGui::TableSetupColumn("New Bytes", ImGuiTableColumnFlags_WidthStretch);
					ImGui::TableSetupColumn("Size", ImGuiTableColumnFlags_WidthFixed, 60);
					ImGui::TableHeadersRow();

					// Display log entries
					for (size_t i = 0; i < writeLog.size() && i < maxLogEntries; ++i) {
						const auto& entry = writeLog[i];
						ImGui::TableNextRow();

						ImGui::TableNextColumn();
						ImGui::Text("%s", entry.timestamp.c_str());

						ImGui::TableNextColumn();
						ImGui::Text("%s", MemoryMonitor::FormatAddress(entry.address).c_str());

						ImGui::TableNextColumn();
						ImGui::Text("%s", MemoryMonitor::FormatBytes(entry.oldBytes).c_str());

						ImGui::TableNextColumn();
						ImGui::Text("%s", MemoryMonitor::FormatBytes(entry.newBytes).c_str());

						ImGui::TableNextColumn();
						ImGui::Text("%zu", entry.size);
					}

					if (autoScroll && !writeLog.empty()) {
						ImGui::SetScrollHereY(1.0f);
					}

					ImGui::EndTable();
				}
			}
			ImGui::EndChild();

			ImGui::Separator();
			ImGui::Text("Application average %.3f ms/frame (%.1f FPS)", 1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
		}
		ImGui::End();
	}

	#ifdef _WINDLL
	if (GetAsyncKeyState(VK_INSERT) & 1)
		bDraw = !bDraw;
	#endif
}
