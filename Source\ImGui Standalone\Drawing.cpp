#include "Drawing.h"
#include "MemoryMonitor.h"
#include "APIHook.h"
#include <sstream>
#include <iomanip>
#include <fstream>

LPCSTR Drawing::lpWindowName = "Array of Bytes Monitor - Cheat Engine Detection";
ImVec2 Drawing::vWindowSize = { 900, 700 };
ImGuiWindowFlags Drawing::WindowFlags = 0;
bool Drawing::bDraw = true;

// Static variables for UI state
static bool showAddRegionDialog = false;
static char addressInput[32] = "";
static char sizeInput[16] = "4096";
static bool autoScroll = true;
static int maxLogEntries = 1000;

void Drawing::Active()
{
	bDraw = true;
}

bool Drawing::isActive()
{
	return bDraw == true;
}

void Drawing::Draw()
{
	if (isActive())
	{
		ImGui::SetNextWindowSize(vWindowSize, ImGuiCond_Once);
		ImGui::SetNextWindowBgAlpha(1.0f);
		ImGui::Begin(lpWindowName, &bDraw, WindowFlags);
		{
			// Status section
			ImGui::Text("Array of Bytes Monitor - Detecting your Cheat Engine modifications");
			ImGui::Text("Search & Replace operations will be captured here");
			ImGui::Separator();

			// Hook status
			if (APIHook::IsHooked()) {
				ImGui::TextColored(ImVec4(0, 1, 0, 1), "Status: HOOKS ACTIVE");
			} else {
				ImGui::TextColored(ImVec4(1, 0, 0, 1), "Status: HOOKS INACTIVE");
			}

			ImGui::SameLine();
			ImGui::Text("| Process ID: %d", GetCurrentProcessId());
			ImGui::SameLine();
			ImGui::Text("| API Calls Logged: %zu", APIHook::GetLogCount());

			// Control buttons
			ImGui::Separator();
			if (ImGui::Button("Install API Hooks")) {
				APIHook::InstallHooks();
			}
			ImGui::SameLine();
			if (ImGui::Button("Remove Hooks")) {
				APIHook::RemoveHooks();
			}
			ImGui::SameLine();
			if (ImGui::Button("Clear Log")) {
				APIHook::ClearAPILog();
			}
			ImGui::SameLine();
			if (ImGui::Button("Save Log")) {
				// Save API log to file
				std::ofstream file("api_log.txt");
				if (file.is_open()) {
					const auto& log = APIHook::GetAPILog();
					for (const auto& entry : log) {
						file << entry.timestamp << " | " << entry.apiName << " | "
							 << APIHook::FormatAddress(entry.targetAddress) << " | Size: " << entry.size;
						if (!entry.data.empty()) {
							file << " | Data: " << APIHook::FormatBytes(entry.data);
						}
						file << "\n";
					}
					file.close();
				}
			}

			// Add Region Dialog
			if (showAddRegionDialog) {
				ImGui::OpenPopup("Add Memory Region");
			}

			if (ImGui::BeginPopupModal("Add Memory Region", &showAddRegionDialog, ImGuiWindowFlags_AlwaysAutoResize)) {
				ImGui::Text("Enter memory region to monitor:");
				ImGui::InputText("Address (hex)", addressInput, sizeof(addressInput));
				ImGui::InputText("Size (bytes)", sizeInput, sizeof(sizeInput));

				if (ImGui::Button("Add")) {
					try {
						PVOID address = (PVOID)std::stoull(addressInput, nullptr, 16);
						SIZE_T size = std::stoull(sizeInput);
						if (MemoryMonitor::AddMemoryRegion(address, size)) {
							showAddRegionDialog = false;
							memset(addressInput, 0, sizeof(addressInput));
							strcpy_s(sizeInput, "4096");
						}
					} catch (...) {
						// Invalid input
					}
				}
				ImGui::SameLine();
				if (ImGui::Button("Cancel")) {
					showAddRegionDialog = false;
				}
				ImGui::EndPopup();
			}

			// Settings and Debug Info
			ImGui::Separator();
			ImGui::Checkbox("Auto Scroll", &autoScroll);
			ImGui::SameLine();
			ImGui::SliderInt("Max Log Entries", &maxLogEntries, 100, 10000);

			// Debug information
			ImGui::Text("Debug Info:");
			ImGui::Text("Current Process: %d", GetCurrentProcessId());
			ImGui::Text("Thread ID: %d", GetCurrentThreadId());

			// Test button for manual memory write
			if (ImGui::Button("Test Memory Write")) {
				static int testValue = 0;
				testValue++;
				// This should trigger our monitoring if a region is added
			}

			// Array of Bytes modifications log
			ImGui::Separator();
			ImGui::Text("Live Array of Bytes Detection:");
			ImGui::Text("Example: F0 B5 03 AF 4D F8 → 00 00 A0 E3 1E FF 2F E1");

			if (ImGui::BeginChild("LogRegion", ImVec2(0, 400), true)) {
				const auto& apiLog = APIHook::GetAPILog();

				// Table headers
				if (ImGui::BeginTable("APILogTable", 6, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_ScrollY)) {
					ImGui::TableSetupColumn("Time", ImGuiTableColumnFlags_WidthFixed, 80);
					ImGui::TableSetupColumn("API", ImGuiTableColumnFlags_WidthFixed, 120);
					ImGui::TableSetupColumn("Address", ImGuiTableColumnFlags_WidthFixed, 120);
					ImGui::TableSetupColumn("Size", ImGuiTableColumnFlags_WidthFixed, 60);
					ImGui::TableSetupColumn("Data", ImGuiTableColumnFlags_WidthStretch);
					ImGui::TableSetupColumn("Thread", ImGuiTableColumnFlags_WidthFixed, 60);
					ImGui::TableHeadersRow();

					// Display log entries (newest first)
					for (int i = (int)apiLog.size() - 1; i >= 0 && i >= (int)apiLog.size() - maxLogEntries; --i) {
						const auto& entry = apiLog[i];
						ImGui::TableNextRow();

						ImGui::TableNextColumn();
						ImGui::Text("%s", entry.timestamp.c_str());

						ImGui::TableNextColumn();
						// Color code API calls
						ImVec4 apiColor = ImVec4(1, 1, 1, 1); // White default
						if (entry.apiName == "WriteProcessMemory") apiColor = ImVec4(1, 0.3f, 0.3f, 1); // Red
						else if (entry.apiName == "ReadProcessMemory") apiColor = ImVec4(0.3f, 1, 0.3f, 1); // Green
						else if (entry.apiName.find("VirtualQuery") != std::string::npos) apiColor = ImVec4(0.3f, 0.3f, 1, 1); // Blue
						ImGui::TextColored(apiColor, "%s", entry.apiName.c_str());

						ImGui::TableNextColumn();
						// Highlight the address in yellow
						ImGui::TextColored(ImVec4(1, 1, 0, 1), "%s", APIHook::FormatAddress(entry.targetAddress).c_str());

						ImGui::TableNextColumn();
						ImGui::Text("%zu", entry.size);

						ImGui::TableNextColumn();
						// Show data if available
						if (!entry.data.empty()) {
							ImGui::TextColored(ImVec4(0.8f, 0.8f, 1, 1), "%s", APIHook::FormatBytes(entry.data, 24).c_str());
						} else {
							ImGui::Text("-");
						}

						ImGui::TableNextColumn();
						ImGui::Text("%d", entry.threadId);
					}

					if (autoScroll && !apiLog.empty()) {
						ImGui::SetScrollHereY(1.0f);
					}

					ImGui::EndTable();
				}
			}
			ImGui::EndChild();

			ImGui::Separator();

			// Statistics
			const auto& apiLog = APIHook::GetAPILog();
			if (!apiLog.empty()) {
				ImGui::Text("Statistics:");
				ImGui::Text("Total API Calls: %zu", apiLog.size());

				// Count different API types
				int virtualQueryCount = 0, readCount = 0, writeCount = 0;
				for (const auto& entry : apiLog) {
					if (entry.apiName.find("VirtualQuery") != std::string::npos) virtualQueryCount++;
					else if (entry.apiName == "ReadProcessMemory") readCount++;
					else if (entry.apiName == "WriteProcessMemory") writeCount++;
				}

				ImGui::Text("VirtualQuery calls: %d | Read calls: %d | Write calls: %d",
					virtualQueryCount, readCount, writeCount);

				if (!apiLog.empty()) {
					ImGui::Text("Last API: %s at %s",
						apiLog.back().apiName.c_str(),
						apiLog.back().timestamp.c_str());
				}
			}

			ImGui::Separator();
			ImGui::Text("Performance: %.3f ms/frame (%.1f FPS)", 1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
			ImGui::Text("Detecting Cheat Engine, scanners, and memory tools!");
		}
		ImGui::End();
	}

	#ifdef _WINDLL
	if (GetAsyncKeyState(VK_INSERT) & 1)
		bDraw = !bDraw;
	#endif
}
