#include "MemoryMonitor.h"
#include <sstream>
#include <iomanip>
#include <fstream>
#include <chrono>
#include <ctime>
#include <algorithm>

// Static member initialization
std::vector<MemoryWriteInfo> MemoryMonitor::writeLog;
std::mutex MemoryMonitor::logMutex;
PVOID MemoryMonitor::vectoredHandler = nullptr;
std::vector<MEMORY_BASIC_INFORMATION> MemoryMonitor::monitoredRegions;
bool MemoryMonitor::isMonitoring = false;
HANDLE MemoryMonitor::targetProcess = nullptr;
DWORD MemoryMonitor::targetProcessId = 0;

bool MemoryMonitor::Initialize() {
    if (vectoredHandler != nullptr) {
        return true; // Already initialized
    }

    // Add vectored exception handler
    vectoredHandler = AddVectoredExceptionHandler(1, VectoredExceptionHandler);
    if (vectoredHandler == nullptr) {
        return false;
    }

    // Get current process info
    targetProcessId = GetCurrentProcessId();
    targetProcess = GetCurrentProcess();

    return true;
}

void MemoryMonitor::Shutdown() {
    StopMonitoring();
    
    if (vectoredHandler != nullptr) {
        RemoveVectoredExceptionHandler(vectoredHandler);
        vectoredHandler = nullptr;
    }

    ClearMemoryRegions();
    ClearWriteLog();
}

bool MemoryMonitor::StartMonitoring(DWORD processId) {
    if (isMonitoring) {
        return true;
    }

    if (processId != 0) {
        targetProcessId = processId;
        targetProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (targetProcess == nullptr) {
            return false;
        }
    }

    // Auto-scan and monitor all writable memory regions
    ScanAndMonitorAllRegions();

    isMonitoring = true;
    return true;
}

void MemoryMonitor::ScanAndMonitorAllRegions() {
    MEMORY_BASIC_INFORMATION mbi;
    PVOID address = 0;

    while (VirtualQuery(address, &mbi, sizeof(mbi))) {
        // Monitor writable regions that are committed
        if (mbi.State == MEM_COMMIT &&
            (mbi.Protect & PAGE_READWRITE || mbi.Protect & PAGE_EXECUTE_READWRITE) &&
            !(mbi.Protect & PAGE_GUARD) &&
            mbi.RegionSize > 0 && mbi.RegionSize < 0x10000000) { // Max 256MB per region

            // Add this region to monitoring
            SetPageGuard(mbi.BaseAddress, mbi.RegionSize);
            monitoredRegions.push_back(mbi);
        }

        address = (PVOID)((uintptr_t)mbi.BaseAddress + mbi.RegionSize);
        if ((uintptr_t)address >= 0x7FFFFFFF0000) break; // User space limit
    }
}

void MemoryMonitor::StopMonitoring() {
    if (!isMonitoring) {
        return;
    }

    isMonitoring = false;

    // Remove page guards from all monitored regions
    for (const auto& region : monitoredRegions) {
        RemovePageGuard(region.BaseAddress, region.RegionSize);
    }
}

bool MemoryMonitor::AddMemoryRegion(PVOID startAddress, SIZE_T size) {
    if (!IsValidAddress(startAddress)) {
        return false;
    }

    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(startAddress, &mbi, sizeof(mbi)) == 0) {
        return false;
    }

    // Check if region is already monitored
    for (const auto& region : monitoredRegions) {
        if (region.BaseAddress == mbi.BaseAddress) {
            return true; // Already monitoring this region
        }
    }

    // Set page guard on the memory region
    if (!SetPageGuard(startAddress, size)) {
        return false;
    }

    monitoredRegions.push_back(mbi);
    return true;
}

bool MemoryMonitor::RemoveMemoryRegion(PVOID startAddress) {
    for (auto it = monitoredRegions.begin(); it != monitoredRegions.end(); ++it) {
        if (it->BaseAddress == startAddress) {
            RemovePageGuard(startAddress, it->RegionSize);
            monitoredRegions.erase(it);
            return true;
        }
    }
    return false;
}

void MemoryMonitor::ClearMemoryRegions() {
    for (const auto& region : monitoredRegions) {
        RemovePageGuard(region.BaseAddress, region.RegionSize);
    }
    monitoredRegions.clear();
}

LONG WINAPI MemoryMonitor::VectoredExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo) {
    if (!isMonitoring) {
        return EXCEPTION_CONTINUE_SEARCH;
    }

    PEXCEPTION_RECORD pExceptionRecord = pExceptionInfo->ExceptionRecord;
    PCONTEXT pContext = pExceptionInfo->ContextRecord;

    // Handle guard page exception
    if (pExceptionRecord->ExceptionCode == EXCEPTION_GUARD_PAGE) {
        PVOID faultAddress = (PVOID)pExceptionRecord->ExceptionInformation[1];
        DWORD accessType = (DWORD)pExceptionRecord->ExceptionInformation[0]; // 0=read, 1=write

        // Check if the fault address is in our monitored regions
        if (!IsAddressInMonitoredRegion(faultAddress)) {
            return EXCEPTION_CONTINUE_SEARCH;
        }

        // Log all memory access (read=0, write=1)
        if (accessType == 1) { // Write operation
            // Determine write size based on instruction context
            SIZE_T writeSize = 4; // Default

            // Try to determine actual write size from context
            // This is a simplified approach - could be enhanced
            BYTE* instructionPtr = (BYTE*)pContext->Rip;
            if (instructionPtr) {
                // Check for common write instruction patterns
                if (*instructionPtr == 0x89) writeSize = 4;      // MOV r/m32, r32
                else if (*instructionPtr == 0x66) writeSize = 2; // 16-bit prefix
                else if (*instructionPtr == 0x88) writeSize = 1; // MOV r/m8, r8
                else if (*instructionPtr == 0xC7) writeSize = 4; // MOV r/m32, imm32
            }

            // Create write info entry
            MemoryWriteInfo writeInfo;
            writeInfo.address = faultAddress;
            writeInfo.processId = GetCurrentProcessId();
            writeInfo.threadId = GetCurrentThreadId();
            writeInfo.timestamp = GetCurrentTimestamp();
            writeInfo.size = writeSize;

            // Read current bytes (before write) - read more bytes for context
            SIZE_T contextSize = (writeSize < 16) ? 16 : writeSize;
            ReadMemoryBytes(faultAddress, contextSize, writeInfo.oldBytes);

            // Add to log with placeholder for new bytes
            {
                std::lock_guard<std::mutex> lock(logMutex);
                writeLog.push_back(writeInfo);
            }

            // Temporarily remove page guard to allow the write
            MEMORY_BASIC_INFORMATION mbi;
            VirtualQuery(faultAddress, &mbi, sizeof(mbi));
            DWORD oldProtect;
            VirtualProtect(mbi.BaseAddress, mbi.RegionSize, PAGE_READWRITE, &oldProtect);

            // Set single step flag to catch after the write
            pContext->EFlags |= 0x100;

            return EXCEPTION_CONTINUE_EXECUTION;
        }
    }

    // Handle single step exception (after write completion)
    if (pExceptionRecord->ExceptionCode == EXCEPTION_SINGLE_STEP) {
        // Update the last entry with new bytes
        {
            std::lock_guard<std::mutex> lock(logMutex);
            if (!writeLog.empty()) {
                auto& lastEntry = writeLog.back();

                // Read the same amount of bytes as old bytes for comparison
                SIZE_T readSize = lastEntry.oldBytes.size();
                ReadMemoryBytes(lastEntry.address, readSize, lastEntry.newBytes);

                // Only keep the entry if bytes actually changed
                bool bytesChanged = false;
                if (lastEntry.oldBytes.size() == lastEntry.newBytes.size()) {
                    for (size_t i = 0; i < lastEntry.oldBytes.size(); ++i) {
                        if (lastEntry.oldBytes[i] != lastEntry.newBytes[i]) {
                            bytesChanged = true;
                            break;
                        }
                    }
                }

                // Remove entry if no actual change occurred
                if (!bytesChanged) {
                    writeLog.pop_back();
                }

                // Restore page guard for continued monitoring
                MEMORY_BASIC_INFORMATION mbi;
                VirtualQuery(lastEntry.address, &mbi, sizeof(mbi));
                DWORD oldProtect;
                VirtualProtect(mbi.BaseAddress, mbi.RegionSize, PAGE_READWRITE | PAGE_GUARD, &oldProtect);
            }
        }

        // Clear single step flag
        pContext->EFlags &= ~0x100;

        return EXCEPTION_CONTINUE_EXECUTION;
    }

    return EXCEPTION_CONTINUE_SEARCH;
}

bool MemoryMonitor::SetPageGuard(PVOID address, SIZE_T size) {
    // Align address to page boundary
    SYSTEM_INFO si;
    GetSystemInfo(&si);

    PVOID alignedAddress = (PVOID)((uintptr_t)address & ~((uintptr_t)si.dwPageSize - 1));
    SIZE_T alignedSize = ((uintptr_t)address + size - (uintptr_t)alignedAddress + (uintptr_t)si.dwPageSize - 1) & ~((uintptr_t)si.dwPageSize - 1);

    DWORD oldProtect;
    BOOL result = VirtualProtect(alignedAddress, alignedSize, PAGE_READWRITE | PAGE_GUARD, &oldProtect);

    if (result) {
        // Store the original protection for later restoration
        MEMORY_BASIC_INFORMATION mbi;
        VirtualQuery(alignedAddress, &mbi, sizeof(mbi));
    }

    return result != 0;
}

bool MemoryMonitor::RemovePageGuard(PVOID address, SIZE_T size) {
    DWORD oldProtect;
    return VirtualProtect(address, size, PAGE_READWRITE, &oldProtect) != 0;
}

std::string MemoryMonitor::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    struct tm timeinfo;
    localtime_s(&timeinfo, &time_t);
    ss << std::put_time(&timeinfo, "%H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

void MemoryMonitor::ReadMemoryBytes(PVOID address, SIZE_T size, std::vector<BYTE>& buffer) {
    buffer.resize(size);
    SIZE_T bytesRead;
    if (!ReadProcessMemory(targetProcess, address, buffer.data(), size, &bytesRead)) {
        buffer.clear();
    } else {
        buffer.resize(bytesRead);
    }
}

bool MemoryMonitor::IsAddressInMonitoredRegion(PVOID address) {
    for (const auto& region : monitoredRegions) {
        BYTE* regionStart = (BYTE*)region.BaseAddress;
        BYTE* regionEnd = regionStart + region.RegionSize;
        if (address >= regionStart && address < regionEnd) {
            return true;
        }
    }
    return false;
}

const std::vector<MemoryWriteInfo>& MemoryMonitor::GetWriteLog() {
    return writeLog;
}

void MemoryMonitor::ClearWriteLog() {
    std::lock_guard<std::mutex> lock(logMutex);
    writeLog.clear();
}

size_t MemoryMonitor::GetLogCount() {
    std::lock_guard<std::mutex> lock(logMutex);
    return writeLog.size();
}

void MemoryMonitor::SaveLogToFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(logMutex);
    std::ofstream file(filename);
    if (!file.is_open()) {
        return;
    }

    file << "Memory Write Log - Free Fire Edition\n";
    file << "=====================================\n";
    file << "Time\t\tAddress\t\t\tOld Bytes\t\tNew Bytes\t\tSize\n";
    file << "----\t\t-------\t\t\t---------\t\t---------\t\t----\n";

    for (const auto& entry : writeLog) {
        file << entry.timestamp << "\t"
             << FormatAddress(entry.address) << "\t\t"
             << FormatBytes(entry.oldBytes) << "\t\t"
             << FormatBytes(entry.newBytes) << "\t\t"
             << entry.size << "\n";
    }

    file.close();
}

std::string MemoryMonitor::FormatAddress(PVOID address) {
    std::stringstream ss;
    ss << "0x" << std::hex << std::uppercase << (uintptr_t)address;
    return ss.str();
}

std::string MemoryMonitor::FormatBytes(const std::vector<BYTE>& bytes, size_t maxBytes) {
    std::stringstream ss;
    size_t count = (bytes.size() < maxBytes) ? bytes.size() : maxBytes;
    for (size_t i = 0; i < count; ++i) {
        if (i > 0) ss << " ";
        ss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << (int)bytes[i];
    }
    if (bytes.size() > maxBytes) {
        ss << "...";
    }
    return ss.str();
}

bool MemoryMonitor::IsValidAddress(PVOID address) {
    MEMORY_BASIC_INFORMATION mbi;
    return VirtualQuery(address, &mbi, sizeof(mbi)) != 0 && 
           mbi.State == MEM_COMMIT && 
           (mbi.Protect & PAGE_NOACCESS) == 0;
}
