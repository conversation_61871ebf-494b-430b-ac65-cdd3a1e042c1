#include <windows.h>
#include <iostream>
#include <thread>
#include <chrono>

// Simple test application to generate memory writes for testing the DLL

class TestMemoryArea {
private:
    int* testData;
    size_t size;

public:
    TestMemoryArea(size_t dataSize = 1024) : size(dataSize) {
        // Allocate memory for testing
        testData = new int[size];
        
        // Initialize with some values
        for (size_t i = 0; i < size; ++i) {
            testData[i] = static_cast<int>(i);
        }
        
        std::cout << "Test memory allocated at: 0x" << std::hex << (uintptr_t)testData << std::endl;
        std::cout << "Size: " << std::dec << (size * sizeof(int)) << " bytes" << std::endl;
    }
    
    ~TestMemoryArea() {
        delete[] testData;
    }
    
    void* GetAddress() const {
        return testData;
    }
    
    size_t GetSize() const {
        return size * sizeof(int);
    }
    
    void SimulateGameWrites() {
        std::cout << "\nStarting memory write simulation..." << std::endl;
        
        for (int cycle = 0; cycle < 10; ++cycle) {
            std::cout << "Cycle " << (cycle + 1) << "/10" << std::endl;
            
            // Simulate player position updates
            testData[0] = 100 + cycle;  // X coordinate
            testData[1] = 200 + cycle;  // Y coordinate
            testData[2] = 50 + cycle;   // Z coordinate
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            
            // Simulate health updates
            testData[10] = 100 - (cycle * 5);  // Health decreasing
            
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
            
            // Simulate ammo updates
            testData[20] = 30 - cycle;  // Ammo decreasing
            
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            
            // Simulate score updates
            testData[30] = cycle * 100;  // Score increasing
            
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
        
        std::cout << "Memory write simulation completed!" << std::endl;
    }
    
    void PrintCurrentValues() {
        std::cout << "\nCurrent values:" << std::endl;
        std::cout << "Position: (" << testData[0] << ", " << testData[1] << ", " << testData[2] << ")" << std::endl;
        std::cout << "Health: " << testData[10] << std::endl;
        std::cout << "Ammo: " << testData[20] << std::endl;
        std::cout << "Score: " << testData[30] << std::endl;
    }
};

int main() {
    std::cout << "=== Memory Write Logger Test Application ===" << std::endl;
    std::cout << "Process ID: " << GetCurrentProcessId() << std::endl;
    
    // Create test memory area
    TestMemoryArea testArea(256);  // 256 integers = 1024 bytes
    
    std::cout << "\nInstructions:" << std::endl;
    std::cout << "1. Inject the Memory Write Logger DLL into this process" << std::endl;
    std::cout << "2. Add the memory region: " << testArea.GetAddress() << " (size: " << testArea.GetSize() << ")" << std::endl;
    std::cout << "3. Start monitoring in the DLL" << std::endl;
    std::cout << "4. Press ENTER to start memory write simulation" << std::endl;
    
    std::cin.get();
    
    // Start the simulation
    testArea.SimulateGameWrites();
    
    // Print final values
    testArea.PrintCurrentValues();
    
    std::cout << "\nPress ENTER to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

/*
Compilation instructions:
g++ -o TestMemoryWrites.exe TestMemoryWrites.cpp

Or with Visual Studio:
cl TestMemoryWrites.cpp /Fe:TestMemoryWrites.exe
*/
