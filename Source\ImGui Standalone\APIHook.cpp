#include "APIHook.h"
#include <detours/detours.h>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <ctime>

// Static member initialization
std::vector<APICallInfo> APIHook::apiLog;
std::mutex APIHook::logMutex;
bool APIHook::isHooked = false;

// Original function pointers
SIZE_T (WINAPI* APIHook::OriginalVirtualQuery)(LPCVOID, PMEMORY_BASIC_INFORMATION, SIZE_T) = VirtualQuery;
SIZE_T (WINAPI* APIHook::OriginalVirtualQueryEx)(HANDLE, LPCVOID, PMEMORY_BASIC_INFORMATION, SIZE_T) = VirtualQueryEx;
BOOL (WINAPI* APIHook::OriginalReadProcessMemory)(HANDLE, LPCVOID, LPVOID, SIZE_T, SIZE_T*) = ReadProcessMemory;
BOOL (WINAPI* APIHook::OriginalWriteProcessMemory)(HANDLE, LPVOID, LPCVOID, SIZE_T, SIZE_T*) = WriteProcessMemory;

bool APIHook::InstallHooks() {
    if (isHooked) {
        return true;
    }

    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());
    
    // Hook VirtualQuery
    DetourAttach(&(PVOID&)OriginalVirtualQuery, HookedVirtualQuery);
    
    // Hook VirtualQueryEx
    DetourAttach(&(PVOID&)OriginalVirtualQueryEx, HookedVirtualQueryEx);
    
    // Hook ReadProcessMemory
    DetourAttach(&(PVOID&)OriginalReadProcessMemory, HookedReadProcessMemory);
    
    // Hook WriteProcessMemory
    DetourAttach(&(PVOID&)OriginalWriteProcessMemory, HookedWriteProcessMemory);
    
    LONG result = DetourTransactionCommit();
    
    if (result == NO_ERROR) {
        isHooked = true;
        return true;
    }
    
    return false;
}

bool APIHook::RemoveHooks() {
    if (!isHooked) {
        return true;
    }

    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());
    
    DetourDetach(&(PVOID&)OriginalVirtualQuery, HookedVirtualQuery);
    DetourDetach(&(PVOID&)OriginalVirtualQueryEx, HookedVirtualQueryEx);
    DetourDetach(&(PVOID&)OriginalReadProcessMemory, HookedReadProcessMemory);
    DetourDetach(&(PVOID&)OriginalWriteProcessMemory, HookedWriteProcessMemory);
    
    LONG result = DetourTransactionCommit();
    
    if (result == NO_ERROR) {
        isHooked = false;
        return true;
    }
    
    return false;
}

SIZE_T WINAPI APIHook::HookedVirtualQuery(LPCVOID lpAddress, PMEMORY_BASIC_INFORMATION lpBuffer, SIZE_T dwLength) {
    // Log the query
    LogAPICall("VirtualQuery", (PVOID)lpAddress, dwLength, nullptr, false);
    
    // Call original function
    return OriginalVirtualQuery(lpAddress, lpBuffer, dwLength);
}

SIZE_T WINAPI APIHook::HookedVirtualQueryEx(HANDLE hProcess, LPCVOID lpAddress, PMEMORY_BASIC_INFORMATION lpBuffer, SIZE_T dwLength) {
    // Log the query
    LogAPICall("VirtualQueryEx", (PVOID)lpAddress, dwLength, nullptr, false);
    
    // Call original function
    return OriginalVirtualQueryEx(hProcess, lpAddress, lpBuffer, dwLength);
}

BOOL WINAPI APIHook::HookedReadProcessMemory(HANDLE hProcess, LPCVOID lpBaseAddress, LPVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesRead) {
    // Call original function first
    BOOL result = OriginalReadProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesRead);
    
    // Log the read operation
    if (result && lpBuffer && nSize > 0) {
        LogAPICall("ReadProcessMemory", (PVOID)lpBaseAddress, nSize, lpBuffer, false);
    }
    
    return result;
}

BOOL WINAPI APIHook::HookedWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten) {
    // Log the write operation BEFORE writing
    if (lpBuffer && nSize > 0) {
        LogAPICall("WriteProcessMemory", lpBaseAddress, nSize, lpBuffer, true);
    }
    
    // Call original function
    return OriginalWriteProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesWritten);
}

void APIHook::LogAPICall(const std::string& apiName, PVOID address, SIZE_T size, LPCVOID data, bool isWrite) {
    std::lock_guard<std::mutex> lock(logMutex);
    
    APICallInfo info;
    info.apiName = apiName;
    info.targetAddress = address;
    info.size = size;
    info.processId = GetCurrentProcessId();
    info.threadId = GetCurrentThreadId();
    info.timestamp = GetCurrentTimestamp();
    info.isWrite = isWrite;
    
    // Copy data if available and size is reasonable
    if (data && size > 0 && size <= 1024) {
        info.data.resize(size);
        memcpy(info.data.data(), data, size);
    }
    
    apiLog.push_back(info);
    
    // Keep log size manageable
    if (apiLog.size() > 10000) {
        apiLog.erase(apiLog.begin(), apiLog.begin() + 1000);
    }
}

std::string APIHook::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    struct tm timeinfo;
    localtime_s(&timeinfo, &time_t);
    ss << std::put_time(&timeinfo, "%H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

const std::vector<APICallInfo>& APIHook::GetAPILog() {
    return apiLog;
}

void APIHook::ClearAPILog() {
    std::lock_guard<std::mutex> lock(logMutex);
    apiLog.clear();
}

size_t APIHook::GetLogCount() {
    std::lock_guard<std::mutex> lock(logMutex);
    return apiLog.size();
}

std::string APIHook::FormatAddress(PVOID address) {
    std::stringstream ss;
    ss << "0x" << std::hex << std::uppercase << (uintptr_t)address;
    return ss.str();
}

std::string APIHook::FormatBytes(const std::vector<BYTE>& bytes, size_t maxBytes) {
    std::stringstream ss;
    size_t count = (bytes.size() < maxBytes) ? bytes.size() : maxBytes;
    for (size_t i = 0; i < count; ++i) {
        if (i > 0) ss << " ";
        ss << std::hex << std::uppercase << std::setfill('0') << std::setw(2) << (int)bytes[i];
    }
    if (bytes.size() > maxBytes) {
        ss << "...";
    }
    return ss.str();
}
