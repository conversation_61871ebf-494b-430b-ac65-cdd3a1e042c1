#pragma once
#include "pch.h"
#include <vector>
#include <string>
#include <mutex>

// Alternative approach using API hooking instead of PAGE_GUARD
class MemoryHook {
private:
    static std::mutex hookMutex;
    static bool isHooked;
    static std::vector<PVOID> monitoredAddresses;
    
    // Original function pointers
    static BOOL (WINAPI* OriginalWriteProcessMemory)(HANDLE, LPVOID, LPCVOID, SIZE_T, SIZE_T*);
    static NTSTATUS (NTAPI* OriginalNtWriteVirtualMemory)(HANDLE, PVOID, PVOID, SIZE_T, PSIZE_T);
    
    // Hook functions
    static BOOL WINAPI HookedWriteProcessMemory(HANDLE hProcess, <PERSON><PERSON><PERSON> lpBaseAddress, 
        <PERSON><PERSON><PERSON><PERSON> lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten);
    static NTSTATUS NTAPI HookedNtWriteVirtualMemory(HANDLE ProcessHandle, PVOID BaseAddress,
        PVOID Buffer, SIZE_T NumberOfBytesToWrite, PSIZE_T NumberOfBytesWritten);
    
    // Helper functions
    static bool IsAddressMonitored(PVOID address);
    static void LogWrite(PVOID address, LPCVOID newData, SIZE_T size);
    static std::string GetModuleName(PVOID address);

public:
    static bool InstallHooks();
    static bool RemoveHooks();
    static void AddMonitoredAddress(PVOID address);
    static void RemoveMonitoredAddress(PVOID address);
    static void ClearMonitoredAddresses();
    static bool IsHookInstalled() { return isHooked; }
    static size_t GetMonitoredCount() { return monitoredAddresses.size(); }
};
