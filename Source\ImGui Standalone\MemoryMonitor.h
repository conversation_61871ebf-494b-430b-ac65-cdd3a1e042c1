#pragma once
#include "pch.h"
#include <vector>
#include <string>
#include <mutex>
#include <memory>

// Structure to hold memory write information
struct MemoryWriteInfo {
    PVOID address;
    std::vector<BYTE> oldBytes;
    std::vector<BYTE> newBytes;
    DWORD processId;
    DWORD threadId;
    std::string timestamp;
    SIZE_T size;
};

class MemoryMonitor {
private:
    static std::vector<MemoryWriteInfo> writeLog;
    static std::mutex logMutex;
    static PVOID vectoredHandler;
    static std::vector<MEMORY_BASIC_INFORMATION> monitoredRegions;
    static bool isMonitoring;
    static HANDLE targetProcess;
    static DWORD targetProcessId;

    // Internal functions
    static LONG WINAPI VectoredExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo);
    static bool SetPageGuard(PVOID address, SIZE_T size);
    static bool RemovePageGuard(PVOID address, SIZE_T size);
    static std::string GetCurrentTimestamp();
    static void ReadMemoryBytes(PVOID address, SIZE_T size, std::vector<BYTE>& buffer);
    static bool IsAddressInMonitoredRegion(PVOID address);
    static void ScanAndMonitorAllRegions();
    static void ScanExecutableRegions();

public:
    // Main functions
    static bool Initialize();
    static void Shutdown();
    static bool StartMonitoring(DWORD processId = 0);
    static void StopMonitoring();
    static bool AddMemoryRegion(PVOID startAddress, SIZE_T size);
    static bool RemoveMemoryRegion(PVOID startAddress);
    static void ClearMemoryRegions();
    
    // Logging functions
    static const std::vector<MemoryWriteInfo>& GetWriteLog();
    static void ClearWriteLog();
    static size_t GetLogCount();
    static void SaveLogToFile(const std::string& filename);
    
    // Status functions
    static bool IsMonitoring() { return isMonitoring; }
    static size_t GetMonitoredRegionCount() { return monitoredRegions.size(); }
    static DWORD GetTargetProcessId() { return targetProcessId; }
    
    // Utility functions
    static std::string FormatAddress(PVOID address);
    static std::string FormatBytes(const std::vector<BYTE>& bytes, size_t maxBytes = 16);
    static bool IsValidAddress(PVOID address);
};
