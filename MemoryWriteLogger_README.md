# Memory Write Logger DLL - Free Fire Edition

## 📋 الوصف

هذا الـ DLL هو أداة مراقبة الذاكرة المطورة خصيصاً لمراقبة عمليات الكتابة في الذاكرة داخل محاكيات الألعاب مثل LDPlayer أو BlueStacks عند تشغيل لعبة Free Fire.

## 🎯 الميزات الرئيسية

- **مراقبة الذاكرة في الوقت الفعلي**: يستخدم تقنيات PAGE_GUARD و Vectored Exception Handler
- **تسجيل شامل**: يحفظ العنوان، القيم القديمة، القيم الجديدة، والوقت لكل عملية كتابة
- **واجهة مستخدم بصرية**: واجهة ImGui سهلة الاستخدام لعرض البيانات
- **حفظ السجلات**: إمكانية حفظ السجلات في ملف نصي
- **مراقبة مناطق محددة**: إضافة وإزالة مناطق الذاكرة المراد مراقبتها

## 🛠️ التقنيات المستخدمة

1. **VirtualProtect + PAGE_GUARD**: لحماية مناطق الذاكرة ومراقبة الوصول إليها
2. **Vectored Exception Handler**: لالتقاط استثناءات EXCEPTION_GUARD_PAGE
3. **ImGui + DirectX 11**: لواجهة المستخدم الرسومية
4. **Multi-threading**: للأداء المحسن

## 📁 الملفات المهمة

- `ImGui Standalone.dll` - الملف الرئيسي للـ DLL
- `MemoryMonitor.h/cpp` - نظام مراقبة الذاكرة
- `Drawing.h/cpp` - واجهة المستخدم
- `memory_log.txt` - ملف السجل المحفوظ

## 🚀 كيفية الاستخدام

### 1. حقن الـ DLL

استخدم أي أداة حقن DLL مثل:
- Process Hacker
- Cheat Engine
- DLL Injector مخصص

```bash
# مثال باستخدام rundll32 (للاختبار فقط)
rundll32.exe "ImGui Standalone.dll",DllMain
```

### 2. التحكم في الواجهة

- **INSERT**: إظهار/إخفاء الواجهة
- **END**: إغلاق الأداة (في وضع EXE)

### 3. إضافة مناطق للمراقبة

1. اضغط على "Add Region"
2. أدخل العنوان بصيغة hex (مثل: 0x1C6B2A05A80)
3. أدخل الحجم بالبايت (مثل: 4096)
4. اضغط "Add"

### 4. بدء المراقبة

1. اضغط "Start Monitoring"
2. ستظهر عمليات الكتابة في الجدول
3. استخدم "Save Log" لحفظ النتائج

## 📊 تفسير النتائج

```yaml
[!] Write Detected
Time: 14:30:25.123
Address: 0x1C6B2A05A80
Old Bytes: 05 00 00 00
New Bytes: 00 00 00 01
Size: 4
```

- **Time**: وقت حدوث الكتابة
- **Address**: عنوان الذاكرة المعدل
- **Old Bytes**: القيم قبل التعديل
- **New Bytes**: القيم بعد التعديل
- **Size**: حجم البيانات المعدلة

## ⚠️ تحذيرات مهمة

1. **الأداء**: قد يؤثر على أداء اللعبة إذا تم مراقبة مناطق كبيرة
2. **الاستقرار**: اختبر على عملية آمنة أولاً
3. **الصلاحيات**: يحتاج صلاحيات إدارية للوصول لذاكرة العمليات الأخرى
4. **مكافح الفيروسات**: قد يتم اكتشافه كبرنامج مشبوه

## 🔧 إعدادات متقدمة

### تخصيص المراقبة

```cpp
// في الكود المصدري
MemoryMonitor::AddMemoryRegion((PVOID)0x1C6B2A05A80, 4096);
MemoryMonitor::StartMonitoring();
```

### تصفية النتائج

- استخدم "Max Log Entries" للتحكم في عدد الإدخالات
- فعل "Auto Scroll" لمتابعة آخر الأحداث

## 🎮 استخدام مع Free Fire

### مناطق مهمة للمراقبة:

1. **إحداثيات اللاعب**: عادة في مناطق الذاكرة المخصصة للـ game state
2. **الصحة/الدم**: قيم float أو integer
3. **الذخيرة**: عدادات الرصاص
4. **معلومات الأعداء**: مواقع وحالة اللاعبين الآخرين

### نصائح للعثور على العناوين:

1. استخدم Cheat Engine للبحث عن القيم
2. راقب التغييرات أثناء اللعب
3. استخدم "Unknown initial value" للبحث
4. فلتر النتائج بناءً على التغييرات

## 🛡️ الأمان والقانونية

- هذه الأداة للأغراض التعليمية والبحثية فقط
- لا تستخدمها للغش في الألعاب الرسمية
- احترم شروط الخدمة للألعاب
- استخدمها بمسؤولية

## 📞 الدعم والتطوير

للمساعدة أو التطوير الإضافي، يمكن تعديل الكود المصدري حسب الحاجة.

### ميزات مستقبلية مقترحة:

- [ ] دعم MinHook للـ API hooking
- [ ] فلترة متقدمة للنتائج
- [ ] تصدير البيانات بصيغ مختلفة
- [ ] واجهة ويب للمراقبة عن بُعد
- [ ] دعم عمليات متعددة

---

**تم التطوير باستخدام**: Visual Studio 2022, ImGui, DirectX 11
