#pragma once
#include "pch.h"
#include <vector>
#include <string>
#include <mutex>

// Structure to log API calls
struct APICallInfo {
    std::string apiName;
    PVOID targetAddress;
    SIZE_T size;
    std::vector<BYTE> data;
    DWORD processId;
    DWORD threadId;
    std::string timestamp;
    bool isWrite;
};

class APIHook {
private:
    static std::vector<APICallInfo> apiLog;
    static std::mutex logMutex;
    static bool isHooked;
    
    // Original function pointers
    static SIZE_T (WINAPI* OriginalVirtualQuery)(LPCVOID, PMEMORY_BASIC_INFORMATION, SIZE_T);
    static SIZE_T (WINAPI* OriginalVirtualQueryEx)(HANDLE, LPCVOID, PMEMORY_BASIC_INFORMATION, SIZE_T);
    static BOOL (WINAPI* OriginalReadProcessMemory)(HANDLE, LPCVOID, LPVOID, SIZE_T, SIZE_T*);
    static BOOL (WINAPI* OriginalWriteProcessMemory)(HANDLE, LPVOID, LPCVOID, SIZE_T, SIZE_T*);
    
    // Hooked functions
    static SIZE_T WINAPI HookedVirtualQuery(LPCVOID lpAddress, PMEMORY_BASIC_INFORMATION lpBuffer, SIZE_T dwLength);
    static SIZE_T WINAPI HookedVirtualQueryEx(HANDLE hProcess, LPCVOID lpAddress, PMEMORY_BASIC_INFORMATION lpBuffer, SIZE_T dwLength);
    static BOOL WINAPI HookedReadProcessMemory(HANDLE hProcess, LPCVOID lpBaseAddress, LPVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesRead);
    static BOOL WINAPI HookedWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T* lpNumberOfBytesWritten);
    
    // Helper functions
    static std::string GetCurrentTimestamp();
    static void LogAPICall(const std::string& apiName, PVOID address, SIZE_T size, LPCVOID data, bool isWrite);
    static bool InstallSingleHook(LPCSTR moduleName, LPCSTR functionName, LPVOID hookFunction, LPVOID* originalFunction);

public:
    static bool InstallHooks();
    static bool RemoveHooks();
    static const std::vector<APICallInfo>& GetAPILog();
    static void ClearAPILog();
    static size_t GetLogCount();
    static bool IsHooked() { return isHooked; }
    static std::string FormatAddress(PVOID address);
    static std::string FormatBytes(const std::vector<BYTE>& bytes, size_t maxBytes = 16);
};
