@echo off
echo === Memory Write Logger - Build and Test Script ===
echo.

REM Check if Visual Studio is available
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo Setting up Visual Studio environment...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
)

echo Building test application...
cl TestMemoryWrites.cpp /Fe:TestMemoryWrites.exe /EHsc
if %errorlevel% neq 0 (
    echo Failed to build test application!
    pause
    exit /b 1
)

echo.
echo Test application built successfully!
echo.
echo Instructions:
echo 1. Run TestMemoryWrites.exe
echo 2. Note the memory address shown
echo 3. Use a DLL injector to inject "ImGui Standalone.dll" into the test process
echo 4. In the DLL interface, add the memory region with the noted address
echo 5. Start monitoring and then press ENTER in the test app
echo.
echo DLL Location: ..\Source\x64\Debug\ImGui Standalone.dll
echo.

pause
echo.
echo Starting test application...
TestMemoryWrites.exe
